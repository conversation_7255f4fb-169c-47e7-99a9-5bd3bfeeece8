/* 
Post States
*/
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';

abstract class PostStates {}

// initial
class PostsInitial extends PostStates {}

// loading...
class PostsLoading extends PostStates {}

// uploading...
class PostsUploading extends PostStates {}

// loaded
class PostsLoaded extends PostStates {
  final List<Post> posts;
  PostsLoaded(this.posts);
}

// success
class PostsSuccess extends PostStates {
  final String message;
  PostsSuccess(this.message);
}

// error
class PostsError extends PostStates {
  final String message;
  PostsError(this.message);
}
