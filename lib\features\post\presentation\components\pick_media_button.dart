import 'package:flutter/material.dart';

class PickMediaButton extends StatelessWidget {
  final bool isDisabled;
  final Function()? onTap;
  final IconData icon;
  final String label;
  final Color? color;
  final Color? textColor;
  final Color? buttonColor;
  final Color? finalTextColor;
  const PickMediaButton({
    super.key,
    required this.onTap,
    required this.icon,
    required this.label,
    this.color,
    this.textColor,
    this.isDisabled = false,
    this.buttonColor,
    this.finalTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: isDisabled ? Colors.grey[200] : buttonColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: isDisabled
              ? null
              : [
                  BoxShadow(
                    color: buttonColor!.withAlpha(100),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isDisabled ? Colors.grey[400] : finalTextColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isDisabled ? Colors.grey[400] : finalTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
