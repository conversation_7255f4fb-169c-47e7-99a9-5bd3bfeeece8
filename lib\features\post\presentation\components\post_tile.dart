import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/components/my_text_field.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/comment_tile.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/entities/profile_user.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/pages/profile_page.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_cost_display.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/pages/post_detail_page.dart';

class PostTile extends StatefulWidget {
  final Post post;
  final void Function()? onDeletePressed;
  const PostTile({
    super.key,
    required this.post,
    required this.onDeletePressed,
  });

  @override
  State<PostTile> createState() => _PostTileState();
}

class _PostTileState extends State<PostTile> {
  // cubits
  late final postCubit = context.read<PostCubit>();
  late final profile = context.read<ProfileCubit>();

  bool isOwnPost = false;
  // Current user
  AppUser? currentUser;

  // post user
  ProfileUser? postUser;

  @override
  void initState() {
    super.initState();
    // get current user
    getCurrrentUser();
    // fetch post user
    fetchPostUser();
  }

  void getCurrrentUser() async {
    final authCubit = context.read<AuthCubit>();
    currentUser = authCubit.currentUser;
    isOwnPost = currentUser?.uid == widget.post.userId;
  }

  void fetchPostUser() async {
    final fetchedUser = await profile.getUserProfile(widget.post.userId);
    if (fetchedUser != null) {
      setState(() {
        postUser = fetchedUser;
      });
    }
  }

  /* 
LIKES
*/
  // user taps like button
  void toggleLikePost() {
    // Current Like status
    final isLiked = widget.post.likes.contains(currentUser!.uid);
    // optimistically update the UI
    setState(() {
      if (isLiked) {
        widget.post.likes.remove(currentUser!.uid); // unlike
      } else {
        widget.post.likes.add(currentUser!.uid); // like
      }
    });
    postCubit.toggleLikePost(widget.post.id, currentUser!.uid).catchError((e) {
      // rollback the UI
      setState(() {
        if (isLiked) {
          widget.post.likes.add(currentUser!.uid); // like
        } else {
          widget.post.likes.remove(currentUser!.uid); // unlike
        }
      });
    });
  }

  /* 
COMMENTS
*/
  // comment text controller
  final commentController = TextEditingController();

  // open comment box  -> user wants to type a new comment

  void openNewCommentBox() {
    // clear text controller
    commentController.clear();
    // show dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: MyTextField(
          controller: commentController,
          hintText: 'Type your comment...',
          obscureText: false,
        ),
        actions: [
          // cancel button
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          // submit button
          TextButton(
            onPressed: () {
              addComment();
              Navigator.of(context).pop();
            },
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }

  // add comment
  void addComment() {
    final newComment = Comment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      postId: widget.post.id,
      userId: currentUser!.uid,
      userName: currentUser!.name,
      text: commentController.text,
      timestamp: DateTime.now(),
    );

    if (commentController.text.isEmpty) {
      return;
    }
    postCubit.addComment(widget.post.id, newComment);
    commentController.clear();
  }

  // show options for deletion
  void showOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Options'),
        content: Text('Are you sure you want to delete this post?'),
        actions: [
          // delete button
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDeletePressed!();
            },
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
          // cancel button
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    commentController.dispose();
    super.dispose();
  }

  // BUILD UI
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.tertiary,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Top section of the post: profile pic/ name/ delete button
          GestureDetector(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ProfilePage(uid: widget.post.userId),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  // profile pic
                  postUser?.profileImageUrl != null
                      ? CachedNetworkImage(
                          imageUrl: postUser?.profileImageUrl ?? '',
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              const CircularProgressIndicator(),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.person),
                          imageBuilder: (context, imageProvider) => Container(
                            height: 40,
                            width: 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        )
                      : Icon(
                          Icons.person,
                          color: Theme.of(context).colorScheme.primary,
                          size: 40,
                        ),
                  const SizedBox(width: 10),
                  // name
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.post.userName,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // date and category formatted
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            timeago.format(
                              widget.post.timestamp,
                              locale: 'en_short',
                            ),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            ' • ',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            widget.post.category.name.replaceFirst(
                              widget.post.category.name[0],
                              widget.post.category.name[0].toUpperCase(),
                            ),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.tertiary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  PostCostDisplay(cost: widget.post.postCost),
                ],
              ),
            ),
          ),
          // Media content (images and video in grid)
          if (widget.post.hasMedia) _buildMediaGrid(),

          // Poll content
          if (widget.post.hasPoll) _buildPollPreview(),

          // Links content
          if (widget.post.hasLinks) _buildLinksPreview(),
          // CAPTION
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 10.0,
            ),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                // // user name
                // Text(
                //   widget.post.userName,
                //   style: TextStyle(fontWeight: FontWeight.bold),
                // ),
                // const SizedBox(width: 10),
                // caption text
                Text(widget.post.text),
                const Spacer(),
                //delete buttons
                if (isOwnPost)
                  GestureDetector(
                    onTap: showOptions,
                    child: Icon(
                      Icons.more_vert,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
              ],
            ),
          ),
          // buttons -> like, comment, timestamp
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 5.0,
            ),
            child: Row(
              children: [
                SizedBox(
                  width: 50,
                  child: Row(
                    children: [
                      // like button
                      GestureDetector(
                        onTap: toggleLikePost,
                        child: Icon(
                          widget.post.likes.contains(currentUser!.uid)
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: widget.post.likes.contains(currentUser!.uid)
                              ? Colors.red
                              : Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        widget.post.likes.length.toString(),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 10),
                // comment button
                GestureDetector(
                  onTap: openNewCommentBox,
                  child: Icon(
                    Icons.comment_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 5),
                Text(
                  widget.post.comments.length.toString(),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {},
                  child: Icon(
                    Icons.share_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          // COMMENT SECTION
          BlocBuilder<PostCubit, PostStates>(
            builder: (context, state) {
              // LOADED
              if (state is PostsLoaded) {
                // final individual post
                final post = state.posts.firstWhere(
                  (p) => p.id == widget.post.id,
                );
                if (post.comments.isNotEmpty) {
                  // How many comments to show
                  int showCommentsCount = post.comments.length;
                  // comment section
                  return Container(
                    // border top
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          color: Theme.of(context).colorScheme.outline,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: showCommentsCount,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        // get comment individually
                        final comment = widget.post.comments[index];
                        return CommentTile(comment: comment);
                      },
                    ),
                  );
                }
              }
              // LOADING...
              if (state is PostsLoading) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is PostsError) {
                return Center(child: Text(state.message));
              } else {
                return const SizedBox();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMediaGrid() {
    final hasImages = widget.post.imageUrls.isNotEmpty;
    final hasVideo = widget.post.videoUrl.isNotEmpty;
    final totalMedia = widget.post.imageUrls.length + (hasVideo ? 1 : 0);

    return GestureDetector(
      onTap: () => _navigateToPostDetail(),
      child: Container(
        height: totalMedia == 1 ? 250 : 200,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        child: totalMedia == 1 ? _buildSingleMedia() : _buildMediaGridLayout(),
      ),
    );
  }

  Widget _buildSingleMedia() {
    if (widget.post.imageUrls.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedNetworkImage(
          imageUrl: widget.post.imageUrls.first,
          height: 250,
          width: double.infinity,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            height: 250,
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: (context, url, error) => Container(
            height: 250,
            color: Theme.of(context).colorScheme.errorContainer,
            child: const Center(child: Icon(Icons.error)),
          ),
        ),
      );
    } else if (widget.post.videoUrl.isNotEmpty) {
      return _buildVideoThumbnail();
    }
    return const SizedBox();
  }

  Widget _buildMediaGridLayout() {
    final hasImages = widget.post.imageUrls.isNotEmpty;
    final hasVideo = widget.post.videoUrl.isNotEmpty;
    final imageCount = widget.post.imageUrls.length;
    final totalMedia = imageCount + (hasVideo ? 1 : 0);

    // Facebook-style layout logic
    if (totalMedia == 2) {
      return _buildTwoMediaLayout();
    } else if (totalMedia == 3) {
      return _buildThreeMediaLayout();
    } else if (totalMedia == 4) {
      return _buildFourMediaLayout();
    } else if (totalMedia >= 5) {
      return _buildFiveOrMoreMediaLayout();
    }

    // Fallback to single media
    return _buildSingleMedia();
  }

  Widget _buildTwoMediaLayout() {
    return Row(
      children: [
        Expanded(child: _getMediaWidget(0)),
        Expanded(child: _getMediaWidget(1)),
      ],
    );
  }

  Widget _buildThreeMediaLayout() {
    return Row(
      children: [
        Expanded(flex: 1, child: _getMediaWidget(0)),
        Expanded(
          flex: 1,
          child: Column(
            children: [
              Expanded(child: _getMediaWidget(1)),
              Expanded(child: _getMediaWidget(2)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFourMediaLayout() {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(child: _getMediaWidget(0)),
              Expanded(child: _getMediaWidget(1)),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(child: _getMediaWidget(2)),
              Expanded(child: _getMediaWidget(3)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFiveOrMoreMediaLayout() {
    final hasImages = widget.post.imageUrls.isNotEmpty;
    final hasVideo = widget.post.videoUrl.isNotEmpty;
    final totalMedia = widget.post.imageUrls.length + (hasVideo ? 1 : 0);

    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(child: _getMediaWidget(0)),
              Expanded(child: _getMediaWidget(1)),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(child: _getMediaWidget(2)),
              Expanded(child: _getMediaWidget(3)),
              Expanded(
                child: Stack(
                  children: [
                    _getMediaWidget(4),
                    if (totalMedia > 5)
                      Container(
                        decoration: BoxDecoration(color: Colors.black54),
                        child: Center(
                          child: Text(
                            '+${totalMedia - 5}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _getMediaWidget(int index) {
    final hasVideo = widget.post.videoUrl.isNotEmpty;
    final imageCount = widget.post.imageUrls.length;

    if (index < imageCount) {
      return _buildImageTile(widget.post.imageUrls[index], index);
    } else if (hasVideo && index == imageCount) {
      return _buildVideoTile();
    }

    return Container(color: Colors.grey.shade300);
  }

  Widget _buildImageTile(String imageUrl, int index) {
    return GestureDetector(
      onTap: () => _navigateToPostDetail(),
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          child: const Center(child: CircularProgressIndicator()),
        ),
        errorWidget: (context, url, error) => Container(
          color: Theme.of(context).colorScheme.errorContainer,
          child: const Center(child: Icon(Icons.error)),
        ),
      ),
    );
  }

  Widget _buildVideoTile() {
    return GestureDetector(
      onTap: () => _navigateToPostDetail(),
      child: Container(
        color: Colors.black,
        child: const Center(
          child: Icon(Icons.play_circle_outline, color: Colors.white, size: 32),
        ),
      ),
    );
  }

  Widget _buildVideoThumbnail() {
    return Container(
      height: 250,
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.play_circle_outline, color: Colors.white, size: 48),
            SizedBox(height: 8),
            Text('Video', style: TextStyle(color: Colors.white)),
          ],
        ),
      ),
    );
  }

  Widget _buildPollPreview() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: GestureDetector(
        onTap: () => _navigateToPostDetail(),
        child: Row(
          children: [
            Icon(Icons.poll, color: Colors.purple.shade700, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Poll • Tap to view and vote',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinksPreview() {
    final linkCount = widget.post.links.length;
    final displayText = linkCount == 1 ? '1 Link' : '$linkCount Links';

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: GestureDetector(
        onTap: () => _navigateToPostDetail(),
        child: Row(
          children: [
            Icon(
              Icons.link,
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '$displayText • Tap to view',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToPostDetail() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailPage(post: widget.post),
      ),
    );
  }
}
