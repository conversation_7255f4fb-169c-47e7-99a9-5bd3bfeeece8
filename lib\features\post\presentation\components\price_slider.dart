import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cost_cubit.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_states.dart';

class PriceSlider extends StatelessWidget {
  const PriceSlider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WalletCubit, WalletStates>(
      builder: (context, walletState) {
        double minValue = 0.05;
        double maxBalance = minValue;

        if (walletState is WalletLoaded) {
          maxBalance = walletState.wallet.balance > minValue
              ? walletState.wallet.balance
              : minValue;
        }

        return BlocBuilder<PostCostCubit, double>(
          builder: (context, currentCost) {
            // Clamp current cost between min & max to avoid assertion error
            double safeValue = currentCost.clamp(minValue, maxBalance);

            // Calculate divisions safely (avoid 0)
            int divisions = ((maxBalance - minValue) * 20).round().clamp(
              1,
              1000,
            );

            return Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'PutUp Price',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                    ),
                  ),
                  // Slider
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Theme.of(context).colorScheme.tertiary,
                      inactiveTrackColor: Theme.of(context).colorScheme.outline,
                      thumbColor: Theme.of(context).colorScheme.tertiary,
                      overlayColor: Theme.of(
                        context,
                      ).colorScheme.tertiary.withAlpha(50),
                      valueIndicatorColor: Theme.of(
                        context,
                      ).colorScheme.primary,
                      valueIndicatorTextStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                      trackHeight: 2,
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 8,
                      ),
                    ),
                    child: Slider(
                      value: safeValue,
                      min: minValue,
                      max: maxBalance,

                      divisions: divisions,
                      onChanged: (value) {
                        context.read<PostCostCubit>().updateCost(value);
                      },
                    ),
                  ),

                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.tertiary,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '\$ ${safeValue.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Balance warning
                  if (walletState is WalletLoaded &&
                      safeValue > walletState.wallet.balance)
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning,
                            size: 16,
                            color: Theme.of(
                              context,
                            ).colorScheme.onErrorContainer,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              'Low balance: \$${walletState.wallet.balance.toStringAsFixed(2)}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onErrorContainer,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
