import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/common/components/custom_icon_button.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/price_slider.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_balance_cubit.dart';
import 'package:social_app_bloc_flutter/globals.dart';

class Sidebar extends StatelessWidget {
  final PostCategory? selectedCategory;
  final Function(PostCategory?) onCategoryChanged;

  const Sidebar({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: kIsWeb ? 140 : 105,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withAlpha(50),
            width: 1,
          ),
        ),
      ),
      child: ListView(
        children: [
          // Profile Section
          _buildProfileSection(context),

          // Account Balance Section
          _buildBalanceSection(context),
          // Price Slider Section
          const PriceSlider(),
          // Categories Section
          _buildCategoriesSection(context),

          // Stats Section
          _buildStatsSection(context),

          const Spacer(),

          // Logout Button
          _buildLogoutButton(context),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileStates>(
      builder: (context, state) {
        if (state is ProfileLoaded) {
          final user = state.profileUser;
          return Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceTint.withAlpha(100),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(6),
                bottomRight: Radius.circular(6),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Profile Picture
                Container(
                  width: 40,
                  height: 40,

                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: user.profileImageUrl.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: user.profileImageUrl,
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              Center(child: const CircularProgressIndicator()),
                          errorWidget: (context, url, error) => Icon(
                            Icons.person,
                            size: 40,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        )
                      : Icon(
                          Icons.person,
                          size: 40,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                ),
                const SizedBox(width: 6),

                Text(
                  user.name,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.secondary,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.all(6),
          child: Center(child: const CircularProgressIndicator()),
        );
      },
    );
  }

  Widget _buildBalanceSection(BuildContext context) {
    return BlocBuilder<WalletBalanceCubit, double>(
      builder: (context, balance) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          padding: const EdgeInsets.all(4),
          width: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceTint.withAlpha(30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Text(
                'Balance',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                '\$${balance.toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 14,
                ),
              ),
              // go to wallet page
              CustomIconButton(
                label: 'ReUp',
                icon: Icons.add,
                backgroundColor: Theme.of(context).colorScheme.tertiary,
                onTap: () => homePageKey.currentState?.switchTab(1),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoriesSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        // boder
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Categories',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),

          // // All Posts Button
          // _buildCategoryButton(
          //   context,
          //   'All Posts',
          //   null,
          //   selectedCategory == null,
          // ),
          const SizedBox(height: 8),

          // Category Buttons
          ...PostCategory.values.map(
            (category) => _buildCategoryButton(
              context,
              category.name.replaceFirst(
                category.name[0],
                category.name[0].toUpperCase(),
              ),
              category,
              selectedCategory == category,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryButton(
    BuildContext context,
    String title,
    PostCategory? category,
    bool isSelected,
  ) {
    return SizedBox(
      width: double.infinity,
      child: GestureDetector(
        onTap: () => onCategoryChanged(category),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          padding: const EdgeInsets.symmetric(vertical: 5),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.tertiary.withAlpha(50)
                : Colors.transparent,
            border: Border.all(
              color: Theme.of(context).colorScheme.primary,
              width: 0.3,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          alignment: Alignment.center,
          child: Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: isSelected
                  ? Theme.of(context).colorScheme.secondary
                  : Theme.of(context).colorScheme.primary,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileStates>(
      builder: (context, state) {
        if (state is ProfileLoaded) {
          final user = state.profileUser;
          return Container(
            // margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
            padding: const EdgeInsets.all(8),
            margin: const EdgeInsets.symmetric(vertical: 10),
            decoration: BoxDecoration(
              // borderRadius: BorderRadius.circular(12),
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                  width: 1,
                ),
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildStatItem(
                  context,
                  'Followers',
                  user.followers.length.toString(),
                ),
                const SizedBox(height: 6),
                _buildStatItem(
                  context,
                  'Following',
                  user.following.length.toString(),
                ),
                // const SizedBox(height: 12),
                // _buildStatItem(context, 'Posts', '0'),
              ],
            ),
          );
        }

        return const SizedBox();
      },
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  // let use CustomIconButton
  Widget _buildLogoutButton(BuildContext context) {
    return CustomIconButton(
      label: 'Logout',
      icon: Icons.logout,
      backgroundColor: Colors.red,
      onTap: () => context.read<AuthCubit>().logout(),
    );
  }
}
