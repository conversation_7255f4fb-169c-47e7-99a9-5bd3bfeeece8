import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/components/my_text_field.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';

import '../../../../common/components/snackbar.dart';
import '../../domain/entities/profile_user.dart';

class EditProfilePage extends StatefulWidget {
  final ProfileUser user;

  const EditProfilePage({super.key, required this.user});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  // mobile Image picker
  PlatformFile? imagePickedFile;
  // web Image picker
  Uint8List? webImage;

  final bioTextController = TextEditingController();
  late PostCategory selectedCategory;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    selectedCategory = widget.user.selectedPostCategory;
    // Set initial bio text if exists
    bioTextController.text = widget.user.bio;
  }

  @override
  void dispose() {
    bioTextController.dispose();
    super.dispose();
  }

  // Pick image method
  Future<void> pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        withData: kIsWeb, // Important for web
      );

      if (result != null) {
        setState(() {
          imagePickedFile = result.files.first;
          if (kIsWeb) {
            webImage = imagePickedFile!.bytes;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        showSnack(context, 'Failed to pick image: $e');
      }
    }
  }

  void updateProfile() async {
    if (_isUpdating) return; // Prevent multiple updates

    setState(() {
      _isUpdating = true;
    });

    try {
      final profileCubit = context.read<ProfileCubit>();

      // Prepare images
      final String uid = widget.user.uid;
      final imageMobilePath = (!kIsWeb && imagePickedFile != null)
          ? imagePickedFile!.path
          : null;
      final imageWebBytes = (kIsWeb && imagePickedFile != null)
          ? imagePickedFile!.bytes
          : null;

      // Prepare bio - only send if changed
      String? bioText;
      final newBio = bioTextController.text.trim();
      if (newBio != (widget.user.bio)) {
        bioText = newBio.isNotEmpty ? newBio : null;
      }

      // Check if category changed
      PostCategory? categoryToUpdate;
      if (selectedCategory != widget.user.selectedPostCategory) {
        categoryToUpdate = selectedCategory;
      }

      // Validation - check if there's anything to update
      if (imagePickedFile == null &&
          bioText == null &&
          categoryToUpdate == null) {
        showSnack(context, 'No changes to save');
        setState(() {
          _isUpdating = false;
        });
        return;
      }

      // Update profile
      await profileCubit.updateProfile(
        uid: uid,
        newBio: bioText,
        imageMobilePath: imageMobilePath,
        imageWebBytes: imageWebBytes,
        newSelectedPostCategory: categoryToUpdate,
      );
    } catch (e) {
      if (mounted) {
        showSnack(context, 'Failed to update profile: $e');
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfileCubit, ProfileStates>(
      builder: (context, state) {
        // profile loading
        if (state is ProfileLoading) {
          return ConstrainedScaffold(
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Updating profile...'),
                ],
              ),
            ),
          );
        }

        // profile error
        if (state is ProfileError) {
          return ConstrainedScaffold(
            appBar: AppBar(
              title: const Text("Edit Profile"),
              centerTitle: true,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error: ${state.message}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        // profile loaded or initial state, show edit form
        return buildEditPage();
      },
      listener: (context, state) {
        if (state is ProfileLoaded) {
          // Show success message and navigate back
          if (mounted) {
            Navigator.pop(context);
          }
          showSnack(context, 'Profile updated successfully');
        } else if (state is ProfileError) {
          // Show error message
          showSnack(context, 'Update failed: ${state.message}');
          if (mounted) {
            setState(() {
              _isUpdating = false;
            });
          }
        }
      },
    );
  }

  Widget buildEditPage() {
    return ConstrainedScaffold(
      appBar: AppBar(
        title: const Text("Edit Profile"),
        centerTitle: true,
        foregroundColor: Theme.of(context).colorScheme.primary,
        actions: [
          // Save button
          IconButton(
            onPressed: _isUpdating ? null : updateProfile,
            icon: _isUpdating
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.save),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile picture
            Center(
              child: Container(
                height: 200,
                width: 200,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary,
                  shape: BoxShape.circle,
                ),
                clipBehavior: Clip.hardEdge,
                child: _buildProfileImage(),
              ),
            ),
            const SizedBox(height: 25),

            // Pick image button
            Center(
              child: MaterialButton(
                onPressed: _isUpdating ? null : pickImage,
                color: Theme.of(context).colorScheme.tertiary,
                child: const Text('Pick Image'),
              ),
            ),

            const SizedBox(height: 25),

            // Bio section
            const Text(
              "Bio",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            MyTextField(
              controller: bioTextController,
              hintText: 'Enter your bio...',
              obscureText: false,
              maxLines: 3,
            ),

            // Category selection
            const SizedBox(height: 25),
            const Text(
              "Your Post Category",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            DropdownButtonFormField<PostCategory>(
              value: selectedCategory,
              decoration: InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              items: PostCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(
                    category.name.toUpperCase(),
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                );
              }).toList(),
              onChanged: _isUpdating
                  ? null
                  : (PostCategory? newValue) {
                      if (newValue != null) {
                        setState(() {
                          selectedCategory = newValue;
                        });
                      }
                    },
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    // Display selected image for mobile
    if (!kIsWeb && imagePickedFile != null) {
      return Image.file(File(imagePickedFile!.path!), fit: BoxFit.cover);
    }

    // Display selected image for web
    if (kIsWeb && webImage != null) {
      return Image.memory(webImage!, fit: BoxFit.cover);
    }

    // Display existing profile pic
    if (widget.user.profileImageUrl.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: widget.user.profileImageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => const CircularProgressIndicator(),
        errorWidget: (context, url, error) => Icon(
          Icons.person,
          size: 72,
          color: Theme.of(context).colorScheme.primary,
        ),
      );
    }

    // Default avatar
    return Icon(
      Icons.person,
      size: 72,
      color: Theme.of(context).colorScheme.primary,
    );
  }
}
