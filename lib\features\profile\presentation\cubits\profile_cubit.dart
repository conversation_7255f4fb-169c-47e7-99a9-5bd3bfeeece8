import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/entities/profile_user.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/repos/profile_repo.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/features/storage/domain/storage_repo.dart';

class ProfileCubit extends Cubit<ProfileStates> {
  final ProfileRepo profileRepo;
  final StorageRepo storageRepo;

  ProfileCubit({required this.profileRepo, required this.storageRepo})
    : super(ProfileInitial());

  // fetch user profile using repo
  Future<void> fetchUserProfile(String uid) async {
    try {
      emit(ProfileLoading());
      final user = await profileRepo.fetchUserProfile(uid);
      if (user != null) {
        emit(ProfileLoaded(user));
      } else {
        emit(ProfileError('User not found'));
      }
    } catch (e) {
      emit(ProfileError("Failed to fetch user: $e"));
    }
  }

  // get user profile on given id -> useful for loading many profiles
  Future<ProfileUser?> getUserProfile(String uid) async {
    try {
      final user = await profileRepo.fetchUserProfile(uid);
      return user;
    } catch (e) {
      return null;
    }
  }

  // update bio and profile picture
  Future<void> updateProfile({
    required String uid,
    String? newBio,
    Uint8List? imageWebBytes,
    String? imageMobilePath,
    PostCategory? newSelectedPostCategory,
    double? newPostAmount,
  }) async {
    // Only emit loading if we're doing image upload
    if (imageWebBytes != null || imageMobilePath != null) {
      emit(ProfileLoading());
    }

    try {
      // fetch current user
      final currentUser = await profileRepo.fetchUserProfile(uid);

      if (currentUser == null) {
        emit(ProfileError('Failed to fetch user for Profile update'));
        return;
      }

      // Optimistic update for immediate UI response (category and post amount)
      if (newSelectedPostCategory != null || newPostAmount != null) {
        final optimisticProfile = currentUser.copyWith(
          newSelectedPostCategory: newSelectedPostCategory,
          newPostAmount: newPostAmount,
        );
        emit(ProfileLoaded(optimisticProfile));
      }

      // profile picture update
      String? imageDownloadUrl;

      // ensure there is an image to upload
      if (imageWebBytes != null || imageMobilePath != null) {
        try {
          // for mobile
          if (imageMobilePath != null) {
            // upload
            imageDownloadUrl = await storageRepo.uploadProfileImageMobile(
              imageMobilePath,
              uid,
            );
          }
          // for web
          else if (imageWebBytes != null) {
            // upload
            imageDownloadUrl = await storageRepo.uploadProfileImageWeb(
              imageWebBytes,
              uid,
            );
          }

          if (imageDownloadUrl == null) {
            emit(ProfileError('Failed to upload image'));
            return;
          }
        } catch (e) {
          emit(ProfileError('Image upload failed: $e'));
          return;
        }
      }

      // Create updated profile with proper null handling
      final updatedProfile = currentUser.copyWith(
        newBio: newBio, // Allow null to keep existing bio
        newProfileImageUrl:
            imageDownloadUrl, // Allow null to keep existing image
        newSelectedPostCategory:
            newSelectedPostCategory, // Allow null to keep existing category
        newPostAmount: newPostAmount, // Allow null to keep existing post amount
      );

      // update in repo
      await profileRepo.updateProfile(updatedProfile);

      // Emit the updated profile directly instead of refetching
      emit(ProfileLoaded(updatedProfile));
    } catch (e) {
      emit(ProfileError('Error updating Profile: $e'));
      // Add debug logging
      if (kDebugMode) {
        print('ProfileCubit updateProfile error: $e');
      }
    }
  }

  // toggle follow/unfollow
  Future<void> toggleFollow(String currentUid, String targetUid) async {
    try {
      // update in repo
      await profileRepo.toggleFollow(currentUid, targetUid);

      // Refresh the target user's profile to show updated follower count
      await fetchUserProfile(targetUid);
    } catch (e) {
      emit(ProfileError('Failed to toggle follow: $e'));
    }
  }
}
