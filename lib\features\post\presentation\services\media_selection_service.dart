import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

class MediaFile {
  final String? path;
  final Uint8List? bytes;
  final String name;
  final MediaType type;

  MediaFile({this.path, this.bytes, required this.name, required this.type});

  bool get isWeb => kIsWeb;
}

enum MediaType { image, video }

class MediaSelectionService {
  static final ImagePicker _imagePicker = ImagePicker();

  // Capture single image from camera
  static Future<MediaFile?> captureImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: image.name,
            type: MediaType.image,
          );
        } else {
          return MediaFile(
            path: image.path,
            name: image.name,
            type: MediaType.image,
          );
        }
      }
    } catch (e) {
      print('Error capturing image from camera: $e');
    }
    return null;
  }

  // Select single image from gallery
  static Future<MediaFile?> selectImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: image.name,
            type: MediaType.image,
          );
        } else {
          return MediaFile(
            path: image.path,
            name: image.name,
            type: MediaType.image,
          );
        }
      }
    } catch (e) {
      print('Error selecting image from gallery: $e');
    }
    return null;
  }

  // Select multiple images from gallery
  static Future<List<MediaFile>> selectMultipleImagesFromGallery() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      List<MediaFile> mediaFiles = [];
      for (XFile image in images) {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          mediaFiles.add(
            MediaFile(bytes: bytes, name: image.name, type: MediaType.image),
          );
        } else {
          mediaFiles.add(
            MediaFile(
              path: image.path,
              name: image.name,
              type: MediaType.image,
            ),
          );
        }
      }
      return mediaFiles;
    } catch (e) {
      print('Error selecting multiple images: $e');
      return [];
    }
  }

  // Record video from camera
  static Future<MediaFile?> recordVideoFromCamera() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(seconds: 60),
      );

      if (video != null) {
        if (kIsWeb) {
          final bytes = await video.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: video.name,
            type: MediaType.video,
          );
        } else {
          return MediaFile(
            path: video.path,
            name: video.name,
            type: MediaType.video,
          );
        }
      }
    } catch (e) {
      print('Error recording video from camera: $e');
    }
    return null;
  }

  // Select video from gallery
  static Future<MediaFile?> selectVideoFromGallery() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(seconds: 60),
      );

      if (video != null) {
        if (kIsWeb) {
          final bytes = await video.readAsBytes();
          return MediaFile(
            bytes: bytes,
            name: video.name,
            type: MediaType.video,
          );
        } else {
          return MediaFile(
            path: video.path,
            name: video.name,
            type: MediaType.video,
          );
        }
      }
    } catch (e) {
      print('Error selecting video from gallery: $e');
    }
    return null;
  }

  // Get video metadata using chewie (for future implementation)
  static Future<Map<String, dynamic>?> getVideoMetadata(String path) async {
    // This will be implemented when we need video metadata
    // For now, return basic info
    try {
      if (!kIsWeb) {
        final file = File(path);
        final size = await file.length();
        return {'size': size, 'path': path};
      }
    } catch (e) {
      print('Error getting video metadata: $e');
    }
    return null;
  }
}
