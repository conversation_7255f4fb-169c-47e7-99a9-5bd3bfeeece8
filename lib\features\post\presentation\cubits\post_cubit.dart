import 'dart:typed_data';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/domain/repos/post_repo.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/storage/domain/storage_repo.dart';
import 'package:social_app_bloc_flutter/features/wallet/domain/repos/wallet_repo.dart';
import 'package:social_app_bloc_flutter/features/poll/domain/entities/poll.dart'
    as poll_entities;
import 'package:social_app_bloc_flutter/features/poll/domain/repos/poll_repo.dart';

class PostCubit extends Cubit<PostStates> {
  final PostRepo postRepo;
  final StorageRepo storageRepo;
  final WalletRepo walletRepo;
  final PollRepo pollRepo;

  PostCubit({
    required this.postRepo,
    required this.storageRepo,
    required this.walletRepo,
    required this.pollRepo,
  }) : super(PostsInitial());

  // create a new post with multiple images support
  Future<void> createPost(
    Post post, {
    List<String>? imagePaths,
    String? videoPath,
    List<Uint8List>? imageBytes,
    Uint8List? videoBytes,
    String? pollQuestion,
  }) async {
    try {
      emit(PostsUploading());

      List<String> imageUrls = [];
      String? videoUrl;

      // Upload multiple images if provided
      if (imagePaths != null && imagePaths.isNotEmpty) {
        imageUrls = await storageRepo.uploadMultiplePostImagesMobile(
          imagePaths,
          post.id,
        );
      } else if (imageBytes != null && imageBytes.isNotEmpty) {
        imageUrls = await storageRepo.uploadMultiplePostImagesWeb(
          imageBytes,
          post.id,
        );
      }

      // Upload video if provided
      if (videoPath != null) {
        videoUrl = await storageRepo.uploadPostVideoMobile(videoPath, post.id);
      } else if (videoBytes != null) {
        videoUrl = await storageRepo.uploadPostVideoWeb(videoBytes, post.id);
      }

      // Create poll if poll question is provided
      String? pollId;
      if (pollQuestion != null && pollQuestion.isNotEmpty) {
        final poll = poll_entities.Poll(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          question: pollQuestion,
          postId: post.id,
          creatorId: post.userId,
          createdAt: DateTime.now(),
          votes: [],
          allowMultipleVotes: false,
        );

        await pollRepo.createPoll(poll);
        pollId = poll.id;
      }

      // Update post with media URLs and poll ID
      final updatedPost = post.copyWith(
        imageUrls: imageUrls.isNotEmpty ? imageUrls : post.imageUrls,
        videoUrl: videoUrl ?? post.videoUrl,
        pollId: pollId,
      );

      // Deduct wallet **after successful uploads but before saving post**
      await walletRepo.deductBalance(
        updatedPost.userId,
        updatedPost.postCost,
        'Post creation: ${updatedPost.text.substring(0, updatedPost.text.length > 20 ? 20 : updatedPost.text.length)}...',
        'Post Creation',
      );

      // Create post in backend
      await postRepo.createPost(updatedPost);

      // Refetch posts and emit success
      await fetchAllPosts();
      emit(PostsSuccess("Post created successfully"));
    } catch (e) {
      emit(PostsError("Failed to create post: $e"));
    }
  }

  // Legacy method for backward compatibility
  Future<void> createPostLegacy(
    Post post, {
    String? imagePath,
    String? videoPath,
    Uint8List? imageBytes,
    Uint8List? videoBytes,
  }) async {
    return createPost(
      post,
      imagePaths: imagePath != null ? [imagePath] : null,
      videoPath: videoPath,
      imageBytes: imageBytes != null ? [imageBytes] : null,
      videoBytes: videoBytes,
    );
  }

  // fetch all posts
  Future<void> fetchAllPosts() async {
    try {
      emit(PostsLoading());
      final posts = await postRepo.fetchAllPosts();
      emit(PostsLoaded(posts));
    } catch (e) {
      emit(PostsError("Failed to fetch posts: $e"));
    }
  }

  // fetch posts by category
  Future<void> fetchPostsByCategory(PostCategory category) async {
    try {
      emit(PostsLoading());
      final posts = await postRepo.fetchPostsByCategory(category);
      emit(PostsLoaded(posts));
    } catch (e) {
      emit(PostsError("Failed to fetch posts by category: $e"));
    }
  }

  // delete post
  Future<void> deletePost(String postId) async {
    try {
      await postRepo.deletePost(postId);
      await fetchAllPosts();
    } catch (e) {
      emit(PostsError("Failed to delete post: $e"));
    }
  }

  // toggle like post
  Future<void> toggleLikePost(String postId, String userId) async {
    try {
      await postRepo.toggleLikePost(postId, userId);
    } catch (e) {
      emit(PostsError("Failed to like/unlike post: $e"));
    }
  }

  // add comment
  Future<void> addComment(String postId, Comment comment) async {
    try {
      await postRepo.addComment(postId, comment);
      await fetchAllPosts();
    } catch (e) {
      emit(PostsError("Failed to add comment: $e"));
    }
  }

  // delete comment
  Future<void> deleteComment(String postId, String commentId) async {
    try {
      await postRepo.deleteComment(postId, commentId);
      await fetchAllPosts();
    } catch (e) {
      emit(PostsError("Failed to delete comment: $e"));
    }
  }
}
