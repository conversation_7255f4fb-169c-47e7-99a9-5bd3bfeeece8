import 'package:flutter/material.dart';
import 'package:social_app_bloc_flutter/features/poll/domain/entities/poll.dart';

class PollCreationWidget extends StatefulWidget {
  final Function(String question) onPollCreated;
  final VoidCallback? onCancel;

  const PollCreationWidget({
    super.key,
    required this.onPollCreated,
    this.onCancel,
  });

  @override
  State<PollCreationWidget> createState() => _PollCreationWidgetState();
}

class _PollCreationWidgetState extends State<PollCreationWidget> {
  final TextEditingController _questionController = TextEditingController();
  final int _maxQuestionLength = 200;

  @override
  void dispose() {
    _questionController.dispose();
    super.dispose();
  }

  void _createPoll() {
    final question = _questionController.text.trim();
    if (question.isNotEmpty) {
      widget.onPollCreated(question);
      _questionController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.poll,
                color: Colors.purple.shade700,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Create Poll',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.purple.shade700,
                ),
              ),
              const Spacer(),
              if (widget.onCancel != null)
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close),
                  iconSize: 20,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Question input
          TextField(
            controller: _questionController,
            maxLines: 3,
            maxLength: _maxQuestionLength,
            decoration: InputDecoration(
              hintText: 'Ask a question...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
          ),
          const SizedBox(height: 12),
          
          // Poll options preview
          Text(
            'Poll Options:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          // Fixed options display
          ...PollOption.values.map((option) => Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(
                color: _getOptionColor(option).withOpacity(0.3),
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: _getOptionColor(option),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  option.displayName,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          )),
          
          const SizedBox(height: 16),
          
          // Create button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _questionController.text.trim().isNotEmpty 
                  ? _createPoll 
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple.shade700,
                foregroundColor: Colors.white,
              ),
              child: const Text('Create Poll'),
            ),
          ),
        ],
      ),
    );
  }

  Color _getOptionColor(PollOption option) {
    switch (option) {
      case PollOption.yes:
        return Colors.green;
      case PollOption.no:
        return Colors.red;
      case PollOption.dontCare:
        return Colors.orange;
    }
  }
}

// Simple poll preview widget for upload post page
class PollPreviewWidget extends StatelessWidget {
  final String question;
  final VoidCallback? onRemove;

  const PollPreviewWidget({
    super.key,
    required this.question,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.poll,
                color: Colors.purple.shade700,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Poll',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.purple.shade700,
                ),
              ),
              const Spacer(),
              if (onRemove != null)
                IconButton(
                  onPressed: onRemove,
                  icon: const Icon(Icons.close),
                  iconSize: 16,
                  color: Colors.red,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            question,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Options: Yes, No, Don\'t Care',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }
}
