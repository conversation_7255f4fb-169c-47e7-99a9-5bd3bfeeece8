import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';
import 'package:social_app_bloc_flutter/features/poll/presentation/components/poll_display_widget.dart';
import 'package:social_app_bloc_flutter/features/poll/presentation/cubits/poll_cubit.dart';
import 'package:social_app_bloc_flutter/features/poll/presentation/cubits/poll_states.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/components/my_text_field.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/post_cost_display.dart';
import 'package:social_app_bloc_flutter/features/profile/domain/entities/profile_user.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/comment_tile.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/pages/profile_page.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/video_player_widget.dart';

class PostDetailPage extends StatefulWidget {
  final Post post;

  const PostDetailPage({super.key, required this.post});

  @override
  State<PostDetailPage> createState() => _PostDetailPageState();
}

class _PostDetailPageState extends State<PostDetailPage> {
  // Current user
  AppUser? currentUser;

  // Post user profile data
  ProfileUser? postUser;

  // Comment controller
  final commentController = TextEditingController();

  // Cubits
  late final postCubit = context.read<PostCubit>();
  late final profileCubit = context.read<ProfileCubit>();

  @override
  void initState() {
    super.initState();

    // Get current user
    getCurrentUser();

    // Fetch post user profile
    fetchPostUser();

    // Load poll if post has one - use post frame callback to avoid context issues
    if (widget.post.hasPoll) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          context.read<PollCubit>().getPollByPostId(widget.post.id);
        }
      });
    }
  }

  void getCurrentUser() {
    final authCubit = context.read<AuthCubit>();
    currentUser = authCubit.currentUser;
  }

  void fetchPostUser() async {
    final fetchedUser = await profileCubit.getUserProfile(widget.post.userId);
    if (fetchedUser != null && mounted) {
      setState(() {
        postUser = fetchedUser;
      });
    }
  }

  @override
  void dispose() {
    commentController.dispose();
    super.dispose();
  }

  // Like functionality
  void toggleLikePost() {
    if (currentUser == null) return;

    // Current Like status
    final isLiked = widget.post.likes.contains(currentUser!.uid);

    // Optimistically update the UI
    setState(() {
      if (isLiked) {
        widget.post.likes.remove(currentUser!.uid); // unlike
      } else {
        widget.post.likes.add(currentUser!.uid); // like
      }
    });

    // Call the backend
    postCubit.toggleLikePost(widget.post.id, currentUser!.uid).catchError((e) {
      // Rollback the UI on error
      if (mounted) {
        setState(() {
          if (isLiked) {
            widget.post.likes.add(currentUser!.uid); // restore like
          } else {
            widget.post.likes.remove(currentUser!.uid); // restore unlike
          }
        });
      }
    });
  }

  // Comment functionality
  void openNewCommentBox() {
    // Clear text controller
    commentController.clear();
    // Show dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: MyTextField(
          controller: commentController,
          hintText: 'Type your comment...',
          obscureText: false,
        ),
        actions: [
          // Cancel button
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          // Submit button
          TextButton(
            onPressed: () {
              addComment();
              Navigator.of(context).pop();
            },
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  // Add comment
  void addComment() {
    if (currentUser == null || commentController.text.isEmpty) return;

    final newComment = Comment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      postId: widget.post.id,
      userId: currentUser!.uid,
      userName: currentUser!.name,
      text: commentController.text,
      timestamp: DateTime.now(),
    );

    // Optimistically add comment to UI
    setState(() {
      widget.post.comments.add(newComment);
    });

    // Call backend
    postCubit.addComment(widget.post.id, newComment).catchError((e) {
      // Rollback on error
      if (mounted) {
        setState(() {
          widget.post.comments.removeWhere(
            (comment) => comment.id == newComment.id,
          );
        });
      }
    });

    commentController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedScaffold(
      appBar: AppBar(
        title: Text('${widget.post.userName}\'s Post'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: ListView(
        padding: const EdgeInsets.all(12),
        children: [
          // Post header
          _buildPostHeader(),
          const SizedBox(height: 12),
          // Post text
          if (widget.post.text.isNotEmpty) ...[
            _buildPostText(),
            const SizedBox(height: 12),
          ],
          // Media content
          if (widget.post.hasMedia) ...[
            _buildMediaContent(),
            const SizedBox(height: 12),
          ],

          // Poll content
          if (widget.post.hasPoll) ...[
            _buildPollContent(),
            const SizedBox(height: 12),
          ],

          // Links content
          if (widget.post.hasLinks) ...[
            _buildLinksContent(),
            const SizedBox(height: 12),
          ],

          // Post interactions (likes, comments)
          _buildPostInteractions(),
          const SizedBox(height: 16),

          // Comments section
          _buildCommentsSection(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildPostHeader() {
    return // Top section of the post: profile pic/ name/ delete button
    GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProfilePage(uid: widget.post.userId),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            // profile pic
            postUser?.profileImageUrl != null
                ? CachedNetworkImage(
                    imageUrl: postUser?.profileImageUrl ?? '',
                    fit: BoxFit.cover,
                    placeholder: (context, url) =>
                        const CircularProgressIndicator(),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.person),
                    imageBuilder: (context, imageProvider) => Container(
                      height: 40,
                      width: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  )
                : Icon(
                    Icons.person,
                    color: Theme.of(context).colorScheme.primary,
                    size: 40,
                  ),
            const SizedBox(width: 10),
            // name
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.post.userName,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // date and category formatted
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      timeago.format(widget.post.timestamp, locale: 'en_short'),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      ' • ',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      widget.post.category.name.replaceFirst(
                        widget.post.category.name[0],
                        widget.post.category.name[0].toUpperCase(),
                      ),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.tertiary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const Spacer(),
            PostCostDisplay(cost: widget.post.postCost),
          ],
        ),
      ),
    );
  }

  Widget _buildPostText() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(2),

      child: Text(
        widget.post.text,
        style: Theme.of(context).textTheme.bodyLarge,
      ),
    );
  }

  Widget _buildMediaContent() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Images grid
          if (widget.post.imageUrls.isNotEmpty) ...[_buildImagesGrid()],

          // Video
          if (widget.post.videoUrl.isNotEmpty) ...[_buildVideoPlayer()],
        ],
      ),
    );
  }

  Widget _buildImagesGrid() {
    final imageCount = widget.post.imageUrls.length;

    if (imageCount == 1) {
      return GestureDetector(
        onTap: () => _openImageViewer(0),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(2),
          child: CachedNetworkImage(
            imageUrl: widget.post.imageUrls.first,
            height: 300,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 300,
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              child: const Center(child: CircularProgressIndicator()),
            ),
            errorWidget: (context, url, error) => Container(
              height: 300,
              color: Theme.of(context).colorScheme.errorContainer,
              child: const Center(child: Icon(Icons.error)),
            ),
          ),
        ),
      );
    }

    // Grid layout for multiple images
    return GridView.builder(
      padding: const EdgeInsets.all(2),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: imageCount == 2 ? 2 : 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: imageCount,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () => _openImageViewer(index),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: CachedNetworkImage(
              imageUrl: widget.post.imageUrls[index],
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => Container(
                color: Theme.of(context).colorScheme.errorContainer,
                child: const Center(child: Icon(Icons.error)),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildVideoPlayer() {
    return VideoPlayerWidget(
      videoUrl: widget.post.videoUrl,
      autoPlay: false,
      showControls: true,
    );
  }

  Widget _buildPollContent() {
    return BlocBuilder<PollCubit, PollStates>(
      builder: (context, state) {
        if (state is PollLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is PollLoaded) {
          final currentUserId = context.read<AuthCubit>().currentUser?.uid;
          return PollDisplayWidget(
            poll: state.poll,
            currentUserId: currentUserId,
            onVote: (option) {
              if (currentUserId != null) {
                context.read<PollCubit>().voteOnPoll(
                  state.poll.id,
                  currentUserId,
                  option,
                );
              }
            },
          );
        }

        if (state is PollError) {
          return Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.errorContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Error loading poll: ${state.message}',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
          );
        }

        return const SizedBox();
      },
    );
  }

  Widget _buildLinksContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...widget.post.links.map(
          (link) => Container(
            margin: const EdgeInsets.only(bottom: 2),
            child: InkWell(
              onTap: () => _launchUrl(link),
              borderRadius: BorderRadius.circular(2),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withOpacity(0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.link,
                      size: 20,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        link,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          decoration: TextDecoration.underline,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(
                      Icons.open_in_new,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostInteractions() {
    final isLiked =
        currentUser != null && widget.post.likes.contains(currentUser!.uid);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      // padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        // top and bottom border only
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline,
            width: 1,
          ),
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Like button
          GestureDetector(
            onTap: toggleLikePost,
            child: Row(
              children: [
                Icon(
                  isLiked ? Icons.favorite : Icons.favorite_border,
                  color: isLiked
                      ? Colors.red
                      : Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  '${widget.post.likes.length}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 24),

          // Comment button
          GestureDetector(
            onTap: openNewCommentBox,
            child: Row(
              children: [
                Icon(
                  Icons.comment_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  '${widget.post.comments.length}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          // share
          GestureDetector(
            onTap: () {},
            child: Icon(
              Icons.share_outlined,
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsSection() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Comments (${widget.post.comments.length})',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
          if (widget.post.comments.isEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(
                'No comments yet',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            )
          else
            ...widget.post.comments.map(
              (comment) => CommentTile(comment: comment),
            ),
        ],
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open link: $url'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _openImageViewer(int initialIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              '${initialIndex + 1} of ${widget.post.imageUrls.length}',
              style: const TextStyle(color: Colors.white),
            ),
          ),
          body: PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            builder: (BuildContext context, int index) {
              return PhotoViewGalleryPageOptions(
                imageProvider: CachedNetworkImageProvider(
                  widget.post.imageUrls[index],
                ),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained * 0.8,
                maxScale: PhotoViewComputedScale.covered * 2,
              );
            },
            itemCount: widget.post.imageUrls.length,
            loadingBuilder: (context, event) => const Center(
              child: CircularProgressIndicator(color: Colors.white),
            ),
            pageController: PageController(initialPage: initialIndex),
          ),
        ),
      ),
    );
  }
}
