import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/comment.dart';

enum PostCategory { news, politics, sex, entertainment, sports, religion }

enum PostMediaType { images, video, links, poll }

// Import poll entity
// Note: This will be imported when we integrate poll functionality

class PollOption {
  final String id;
  final String text;
  final List<String> votes; // User IDs who voted for this option

  PollOption({required this.id, required this.text, required this.votes});

  Map<String, dynamic> toJson() {
    return {'id': id, 'text': text, 'votes': votes};
  }

  factory PollOption.fromJson(Map<String, dynamic> json) {
    return PollOption(
      id: json['id'],
      text: json['text'],
      votes: List<String>.from(json['votes'] ?? []),
    );
  }

  PollOption copyWith({String? id, String? text, List<String>? votes}) {
    return PollOption(
      id: id ?? this.id,
      text: text ?? this.text,
      votes: votes ?? this.votes,
    );
  }
}

class Poll {
  final String id;
  final String question;
  final List<PollOption> options;
  final DateTime? expiresAt;
  final bool allowMultipleVotes;

  Poll({
    required this.id,
    required this.question,
    required this.options,
    this.expiresAt,
    this.allowMultipleVotes = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options.map((option) => option.toJson()).toList(),
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
      'allowMultipleVotes': allowMultipleVotes,
    };
  }

  factory Poll.fromJson(Map<String, dynamic> json) {
    return Poll(
      id: json['id'],
      question: json['question'],
      options: (json['options'] as List<dynamic>? ?? [])
          .map((optionJson) => PollOption.fromJson(optionJson))
          .toList(),
      expiresAt: json['expiresAt'] != null
          ? (json['expiresAt'] as Timestamp).toDate()
          : null,
      allowMultipleVotes: json['allowMultipleVotes'] ?? false,
    );
  }

  Poll copyWith({
    String? id,
    String? question,
    List<PollOption>? options,
    DateTime? expiresAt,
    bool? allowMultipleVotes,
  }) {
    return Poll(
      id: id ?? this.id,
      question: question ?? this.question,
      options: options ?? this.options,
      expiresAt: expiresAt ?? this.expiresAt,
      allowMultipleVotes: allowMultipleVotes ?? this.allowMultipleVotes,
    );
  }

  int get totalVotes {
    return options.fold(0, (sum, option) => sum + option.votes.length);
  }

  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }
}

class Post {
  final String id;
  final String userId;
  final String userName;
  final String text;
  final List<String> imageUrls; // Changed from single imageUrl to multiple
  final String videoUrl;
  final List<String> links;
  final String? pollId; // Optional poll ID reference
  final DateTime timestamp;
  final List<String> likes;
  final List<Comment> comments;
  final PostCategory category;
  final double postCost;

  Post({
    required this.id,
    required this.userId,
    required this.userName,
    required this.text,
    required this.imageUrls,
    required this.videoUrl,
    required this.links,
    this.pollId,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.category,
    required this.postCost,
  });

  Post copyWith({List<String>? imageUrls, String? videoUrl, String? pollId}) {
    return Post(
      id: id,
      userId: userId,
      userName: userName,
      text: text,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrl: videoUrl ?? this.videoUrl,
      links: links,
      pollId: pollId ?? this.pollId,
      timestamp: timestamp,
      likes: likes,
      comments: comments,
      category: category,
      postCost: postCost,
    );
  }

  // convert post ->  to Json
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'text': text,
      'imageUrls': imageUrls,
      'videoUrl': videoUrl,
      'links': links,
      'pollId': pollId,
      'timestamp': Timestamp.fromDate(timestamp),
      'likes': likes,
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'category': category.name,
      'postCost': postCost,
    };
  }

  factory Post.fromJson(Map<String, dynamic> json) {
    // Prepare comments
    final List<Comment> comments = (json['comments'] as List<dynamic>? ?? [])
        .map((commentJson) => Comment.fromJson(commentJson))
        .toList();

    // Handle backward compatibility - convert old imageUrl to imageUrls list
    List<String> imageUrls = [];
    if (json['imageUrls'] != null) {
      imageUrls = List<String>.from(json['imageUrls']);
    } else if (json['imageUrl'] != null &&
        json['imageUrl'].toString().isNotEmpty) {
      imageUrls = [json['imageUrl']];
    }

    return Post(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      text: json['text'],
      imageUrls: imageUrls,
      videoUrl: json['videoUrl'] ?? '',
      links: List<String>.from(json['links'] ?? []),
      pollId: json['pollId'],
      timestamp: (json['timestamp'] as Timestamp).toDate(),
      likes: List<String>.from(json['likes'] ?? []),
      comments: comments,
      category: PostCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => PostCategory.politics,
      ),
      postCost: (json['postCost'] as num?)?.toDouble() ?? 0.05,
    );
  }

  // Helper methods
  bool get hasPoll => pollId != null && pollId!.isNotEmpty;

  bool get hasMedia => imageUrls.isNotEmpty || videoUrl.isNotEmpty;

  bool get hasLinks => links.isNotEmpty;
}
