import 'package:flutter/material.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/services/media_selection_service.dart';

class MediaPillButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color backgroundColor;
  final VoidCallback onTap;
  final bool isSelected;

  const MediaPillButton({
    super.key,
    required this.icon,
    required this.label,
    required this.backgroundColor,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? backgroundColor
              : backgroundColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: backgroundColor, width: isSelected ? 0 : 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected
                  ? Theme.of(context).colorScheme.surface
                  : backgroundColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: isSelected
                    ? Theme.of(context).colorScheme.surface
                    : backgroundColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MediaPillButtonRow extends StatelessWidget {
  final Function(List<MediaFile>) onImagesSelected;
  final Function(MediaFile) onVideoSelected;
  final Function() onPollSelected;
  final bool hasImages;
  final bool hasVideo;
  final bool hasPoll;

  const MediaPillButtonRow({
    super.key,
    required this.onImagesSelected,
    required this.onVideoSelected,
    required this.onPollSelected,
    this.hasImages = false,
    this.hasVideo = false,
    this.hasPoll = false,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          MediaPillButton(
            icon: Icons.camera_alt,
            label: 'Camera',
            backgroundColor: Colors.blue.shade700,
            isSelected: hasImages,
            onTap: () => _handleCameraSelection(context),
          ),
          const SizedBox(width: 8),
          MediaPillButton(
            icon: Icons.photo_library,
            label: 'Gallery',
            backgroundColor: Colors.green.shade700,
            isSelected: hasImages,
            onTap: () => _handleGallerySelection(context),
          ),
          const SizedBox(width: 8),
          MediaPillButton(
            icon: Icons.videocam,
            label: 'Record',
            backgroundColor: Colors.red.shade700,
            isSelected: hasVideo,
            onTap: () => _handleVideoRecord(context),
          ),
          const SizedBox(width: 8),
          MediaPillButton(
            icon: Icons.video_library,
            label: 'Video',
            backgroundColor: Colors.orange.shade700,
            isSelected: hasVideo,
            onTap: () => _handleVideoGallery(context),
          ),
          const SizedBox(width: 8),
          MediaPillButton(
            icon: Icons.poll,
            label: 'Poll',
            backgroundColor: Colors.purple.shade700,
            isSelected: hasPoll,
            onTap: onPollSelected,
          ),
        ],
      ),
    );
  }

  Future<void> _handleCameraSelection(BuildContext context) async {
    final image = await MediaSelectionService.captureImageFromCamera();
    if (image != null) {
      onImagesSelected([image]);
    }
  }

  Future<void> _handleGallerySelection(BuildContext context) async {
    final images =
        await MediaSelectionService.selectMultipleImagesFromGallery();
    if (images.isNotEmpty) {
      onImagesSelected(images);
    }
  }

  Future<void> _handleVideoRecord(BuildContext context) async {
    final video = await MediaSelectionService.recordVideoFromCamera();
    if (video != null) {
      onVideoSelected(video);
    }
  }

  Future<void> _handleVideoGallery(BuildContext context) async {
    final video = await MediaSelectionService.selectVideoFromGallery();
    if (video != null) {
      onVideoSelected(video);
    }
  }
}
