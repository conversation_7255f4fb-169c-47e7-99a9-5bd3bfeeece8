import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_states.dart';

class WalletBalanceCubit extends Cubit<double> {
  final WalletCubit walletCubit;

  WalletBalanceCubit({required this.walletCubit}) : super(0.0) {
    // Listen to wallet cubit changes
    walletCubit.stream.listen((walletState) {
      if (walletState is WalletLoaded) {
        emit(walletState.wallet.balance);
      }
    });
    
    // Initialize with current state
    final currentState = walletCubit.state;
    if (currentState is WalletLoaded) {
      emit(currentState.wallet.balance);
    }
  }

  void deductOptimistically(double amount) {
    final currentBalance = state;
    if (currentBalance >= amount) {
      emit(currentBalance - amount);
    }
  }

  void addOptimistically(double amount) {
    emit(state + amount);
  }

  void updateBalance(double newBalance) {
    emit(newBalance);
  }
}
