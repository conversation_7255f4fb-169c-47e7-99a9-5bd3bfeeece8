import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/poll/domain/entities/poll.dart';
import 'package:social_app_bloc_flutter/features/poll/domain/repos/poll_repo.dart';
import 'package:social_app_bloc_flutter/features/poll/presentation/cubits/poll_states.dart';

class PollCubit extends Cubit<PollStates> {
  final PollRepo pollRepo;

  PollCubit({required this.pollRepo}) : super(PollInitial());

  // Create a new poll
  Future<void> createPoll({
    required String question,
    required String postId,
    required String creatorId,
    DateTime? expiresAt,
  }) async {
    try {
      emit(PollLoading());

      final poll = Poll(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        question: question,
        postId: postId,
        creatorId: creatorId,
        createdAt: DateTime.now(),
        expiresAt: expiresAt,
        votes: [],
        allowMultipleVotes: false,
      );

      await pollRepo.createPoll(poll);
      emit(PollCreated(poll));
    } catch (e) {
      emit(PollError('Failed to create poll: $e'));
    }
  }

  // Get poll by ID
  Future<void> getPollById(String pollId) async {
    try {
      emit(PollLoading());

      final poll = await pollRepo.getPollById(pollId);
      if (poll != null) {
        emit(PollLoaded(poll));
      } else {
        emit(PollError('Poll not found'));
      }
    } catch (e) {
      emit(PollError('Failed to load poll: $e'));
    }
  }

  // Get poll by post ID
  Future<void> getPollByPostId(String postId) async {
    try {
      emit(PollLoading());

      final poll = await pollRepo.getPollByPostId(postId);
      if (poll != null) {
        emit(PollLoaded(poll));
      } else {
        emit(PollNotFound());
      }
    } catch (e) {
      emit(PollError('Failed to load poll: $e'));
    }
  }

  // Vote on a poll
  Future<void> voteOnPoll(String pollId, String userId, PollOption option) async {
    try {
      // Optimistic update
      if (state is PollLoaded) {
        final currentPoll = (state as PollLoaded).poll;
        final updatedVotes = currentPoll.votes.where((vote) => vote.userId != userId).toList();
        updatedVotes.add(PollVote(
          userId: userId,
          option: option,
          timestamp: DateTime.now(),
        ));
        
        final updatedPoll = currentPoll.copyWith(votes: updatedVotes);
        emit(PollLoaded(updatedPoll));
      }

      await pollRepo.voteOnPoll(pollId, userId, option);
      
      // Refresh poll data
      await getPollById(pollId);
    } catch (e) {
      // Revert optimistic update on error
      await getPollById(pollId);
      emit(PollError('Failed to vote: $e'));
    }
  }

  // Remove vote from poll
  Future<void> removeVote(String pollId, String userId) async {
    try {
      // Optimistic update
      if (state is PollLoaded) {
        final currentPoll = (state as PollLoaded).poll;
        final updatedVotes = currentPoll.votes.where((vote) => vote.userId != userId).toList();
        
        final updatedPoll = currentPoll.copyWith(votes: updatedVotes);
        emit(PollLoaded(updatedPoll));
      }

      await pollRepo.removeVoteFromPoll(pollId, userId);
      
      // Refresh poll data
      await getPollById(pollId);
    } catch (e) {
      // Revert optimistic update on error
      await getPollById(pollId);
      emit(PollError('Failed to remove vote: $e'));
    }
  }

  // Stream poll updates
  void streamPoll(String pollId) {
    try {
      emit(PollLoading());
      
      pollRepo.streamPoll(pollId).listen(
        (poll) {
          if (poll != null) {
            emit(PollLoaded(poll));
          } else {
            emit(PollNotFound());
          }
        },
        onError: (error) {
          emit(PollError('Failed to stream poll: $error'));
        },
      );
    } catch (e) {
      emit(PollError('Failed to start poll stream: $e'));
    }
  }

  // Get polls by creator
  Future<void> getPollsByCreator(String creatorId) async {
    try {
      emit(PollLoading());

      final polls = await pollRepo.getPollsByCreator(creatorId);
      emit(PollsLoaded(polls));
    } catch (e) {
      emit(PollError('Failed to load creator polls: $e'));
    }
  }

  // Get polls voted by user
  Future<void> getPollsVotedByUser(String userId) async {
    try {
      emit(PollLoading());

      final polls = await pollRepo.getPollsVotedByUser(userId);
      emit(PollsLoaded(polls));
    } catch (e) {
      emit(PollError('Failed to load voted polls: $e'));
    }
  }

  // Delete poll
  Future<void> deletePoll(String pollId) async {
    try {
      emit(PollLoading());

      await pollRepo.deletePoll(pollId);
      emit(PollDeleted());
    } catch (e) {
      emit(PollError('Failed to delete poll: $e'));
    }
  }

  // Reset state
  void reset() {
    emit(PollInitial());
  }
}
