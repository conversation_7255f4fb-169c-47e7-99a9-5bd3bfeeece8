import 'dart:io';
import 'dart:typed_data';

import 'package:firebase_storage/firebase_storage.dart';
import 'package:social_app_bloc_flutter/features/storage/domain/storage_repo.dart';

class FirebaseStorageRepo implements StorageRepo {
  final FirebaseStorage firebaseStorage = FirebaseStorage.instance;
  final String profileFolder = 'profile_images_new';
  final String postFolder = 'post_images_new';
  final String videoFolder = 'post_videos_new';
  /*
  PROFILE PICTURES - upload profile pictures to storage
  */
  // mobile platforms
  @override
  Future<String?> uploadProfileImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, profileFolder);
  }

  // web platforms
  @override
  Future<String?> uploadProfileImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, profileFolder);
  }

  /*
  POST PICTURES - upload post pictures to storage
  */
  // mobile platforms
  @override
  Future<String?> uploadPostImageMobile(String path, String fileName) async {
    return _uploadFile(path, fileName, postFolder);
  }

  // web platforms
  @override
  Future<String?> uploadPostImageWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return _uploadFileBytes(fileBytes, fileName, postFolder);
  }

  /// HELPER METHODS - to upload files to storage

  // mobile platform (file)
  Future<String?> _uploadFile(
    String path,
    String fileName,
    String folder,
  ) async {
    try {
      // get file
      final file = File(path);

      // find place to store
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      //  upload
      final uploadTask = await storageRef.putFile(file);
      // get image downlaod url
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      return null;
    }
  }

  // web platform (bytes)
  Future<String?> _uploadFileBytes(
    Uint8List fileBytes,
    String fileName,
    String folder,
  ) async {
    try {
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(
        contentType: 'image/jpeg', // or image/png based on file type
      );

      final uploadTask = await storageRef.putData(fileBytes, metadata);

      final downloadUrl = await uploadTask.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<String?> uploadPostVideoMobile(String path, String fileName) async {
    return await _uploadVideoFile(path, videoFolder, fileName);
  }

  @override
  Future<String?> uploadPostVideoWeb(
    Uint8List fileBytes,
    String fileName,
  ) async {
    return await _uploadVideoBytes(fileBytes, videoFolder, fileName);
  }

  @override
  Future<List<String>> uploadMultiplePostImagesMobile(
    List<String> paths,
    String postId,
  ) async {
    List<String> uploadedUrls = [];

    for (int i = 0; i < paths.length; i++) {
      final fileName = '${postId}_image_$i';
      final url = await _uploadFile(paths[i], fileName, postFolder);
      if (url != null) {
        uploadedUrls.add(url);
      }
    }

    return uploadedUrls;
  }

  @override
  Future<List<String>> uploadMultiplePostImagesWeb(
    List<Uint8List> fileBytes,
    String postId,
  ) async {
    List<String> uploadedUrls = [];

    for (int i = 0; i < fileBytes.length; i++) {
      final fileName = '${postId}_image_$i';
      final url = await _uploadFileBytes(fileBytes[i], fileName, postFolder);
      if (url != null) {
        uploadedUrls.add(url);
      }
    }

    return uploadedUrls;
  }

  Future<String?> _uploadVideoFile(
    String path,
    String folder,
    String fileName,
  ) async {
    try {
      final file = File(path);
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(contentType: 'video/mp4');

      final uploadTask = await storageRef.putFile(file, metadata);
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      return null;
    }
  }

  Future<String?> _uploadVideoBytes(
    Uint8List fileBytes,
    String folder,
    String fileName,
  ) async {
    try {
      final storageRef = firebaseStorage.ref().child("$folder/$fileName");

      final metadata = SettableMetadata(contentType: 'video/mp4');

      final uploadTask = await storageRef.putData(fileBytes, metadata);
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      return null;
    }
  }
}
