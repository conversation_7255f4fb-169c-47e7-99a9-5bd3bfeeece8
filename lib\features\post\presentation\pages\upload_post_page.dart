import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/common/components/snackbar.dart';
import 'package:social_app_bloc_flutter/features/auth/domain/entities/app_user.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/home/<USER>/cubits/category_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/media_pill_button.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/components/link_management_widget.dart';
import 'package:social_app_bloc_flutter/features/poll/presentation/components/poll_creation_widget.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_states.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/services/media_selection_service.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_states.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cost_cubit.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_balance_cubit.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';
import 'package:social_app_bloc_flutter/globals.dart';

class UploadPostPage extends StatefulWidget {
  const UploadPostPage({super.key});

  @override
  State<UploadPostPage> createState() => _UploadPostPageState();
}

class _UploadPostPageState extends State<UploadPostPage> {
  final _textController = TextEditingController();
  AppUser? _currentUser;

  // Media state - now supports all types together
  List<MediaFile> _selectedImages = [];
  List<MediaFile> _selectedVideos = []; // Changed to support multiple videos
  List<String> _links = [];
  String? _pollQuestion; // Poll question if user creates a poll

  @override
  void initState() {
    super.initState();
    _currentUser = context.read<AuthCubit>().currentUser;
    _initializeCostFromProfile();
  }

  void _initializeCostFromProfile() {
    // Set cost from user's profile postAmount
    final profileCubit = context.read<ProfileCubit>();
    final profileState = profileCubit.state;

    if (profileState is ProfileLoaded) {
      final userPostAmount = profileState.profileUser.postAmount;
      context.read<PostCostCubit>().setFixedCost(userPostAmount);
    } else {
      // Default cost if profile not loaded
      context.read<PostCostCubit>().setFixedCost(0.05);
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  // ---------------- MEDIA SELECTION ----------------
  void _handleImagesSelected(List<MediaFile> images) {
    setState(() {
      _selectedImages.addAll(images); // Always append images
    });
    _updateCost();
  }

  void _handleVideoSelected(MediaFile video) {
    setState(() {
      _selectedVideos.add(video); // Always append videos
    });
    _updateCost();
  }

  void _handlePollSelected() {
    if (_pollQuestion != null) {
      // If poll already exists, remove it
      setState(() {
        _pollQuestion = null;
      });
    } else {
      // Show poll creation dialog
      _showPollCreationDialog();
    }
    _updateCost();
  }

  void _showPollCreationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Poll'),
        content: SizedBox(
          width: double.maxFinite,
          child: PollCreationWidget(
            onPollCreated: (question) {
              setState(() {
                _pollQuestion = question;
              });
              Navigator.pop(context);
              _updateCost();
            },
            onCancel: () => Navigator.pop(context),
          ),
        ),
      ),
    );
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
    _updateCost();
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
    _updateCost();
  }

  void _updateLinks(List<String> links) {
    setState(() {
      _links = links;
    });
    _updateCost();
  }

  void _updateCost() {
    // Cost is now fixed - no dynamic calculation needed
    // The cost is set from user's profile when the page loads
  }

  // ---------------- VALIDATION ----------------
  bool _isValidContent(String text) {
    final trimmed = text.trim();
    if (trimmed.isEmpty || trimmed.length < 3 || trimmed.length > 480) {
      return false;
    }
    final cleanText = trimmed.replaceAll(RegExp(r'\s+'), '');
    if (cleanText.length > 1) {
      final firstChar = cleanText[0];
      if (cleanText.split('').every((c) => c == firstChar)) return false;
    }
    return RegExp(r'[a-zA-Z0-9]').hasMatch(trimmed);
  }

  String? _getValidationError(String text) {
    final trimmed = text.trim();
    if (trimmed.isEmpty) return 'Please provide post content';
    if (trimmed.length < 3) return 'Post content must be at least 3 characters';
    if (trimmed.length > 480) return 'Post exceeds 480 characters limit';
    if (!_isValidContent(trimmed)) return 'Please provide meaningful content';
    return null;
  }

  // ---------------- POST UPLOAD ----------------
  Future<void> _uploadPost() async {
    final textError = _getValidationError(_textController.text);
    if (textError != null) {
      showSnack(context, textError);
      return;
    }

    // Check if there's any content (text, images, video, links, or poll)
    if (_textController.text.trim().isEmpty &&
        _selectedImages.isEmpty &&
        _selectedVideos.isEmpty &&
        _links.isEmpty &&
        _pollQuestion == null) {
      showSnack(context, 'Please add some content to your post');
      return;
    }

    final postCost = context.read<PostCostCubit>().state;
    final balance = context.read<WalletBalanceCubit>().state;
    final category = context.read<CategoryCubit>().state;

    if (balance < postCost) {
      showSnack(context, 'Insufficient balance');
      return;
    }

    context.read<WalletBalanceCubit>().deductOptimistically(postCost);

    final newPost = Post(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: _currentUser!.uid,
      userName: _currentUser!.name,
      text: _textController.text.trim(),
      imageUrls: [],
      videoUrl: '',
      links: _links,
      timestamp: DateTime.now(),
      likes: [],
      comments: [],
      category: category ?? PostCategory.politics,
      postCost: postCost,
    );

    final postCubit = context.read<PostCubit>();

    // Prepare media for upload
    List<String>? imagePaths;
    List<Uint8List>? imageBytes;
    String? videoPath;
    Uint8List? videoBytes;

    if (_selectedImages.isNotEmpty) {
      if (kIsWeb) {
        imageBytes = _selectedImages.map((img) => img.bytes!).toList();
      } else {
        imagePaths = _selectedImages.map((img) => img.path!).toList();
      }
    }

    // For now, we'll upload only the first video (can be extended later)
    if (_selectedVideos.isNotEmpty) {
      final firstVideo = _selectedVideos.first;
      if (kIsWeb) {
        videoBytes = firstVideo.bytes;
      } else {
        videoPath = firstVideo.path;
      }
    }

    postCubit.createPost(
      newPost,
      imagePaths: imagePaths,
      imageBytes: imageBytes,
      videoPath: videoPath,
      videoBytes: videoBytes,
      pollQuestion: _pollQuestion,
    );
  }

  void _handlePostSuccess() {
    _textController.clear();
    setState(() {
      _selectedImages.clear();
      _selectedVideos.clear();
      _links.clear();
      _pollQuestion = null;
    });
    _updateCost();
    homePageKey.currentState?.switchTab(0);
  }

  void _handlePostError(String message) {
    final postCost = context.read<PostCostCubit>().state;
    context.read<WalletBalanceCubit>().addOptimistically(postCost);
    showSnack(context, 'Error: $message');
  }

  // ---------------- UI ----------------
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PostCubit, PostStates>(
      listener: (context, state) {
        if (state is PostsLoaded) _handlePostSuccess();
        if (state is PostsError) _handlePostError(state.message);
      },
      builder: (context, state) {
        if (state is PostsLoading || state is PostsUploading) {
          return ConstrainedScaffold(
            body: const Center(child: CircularProgressIndicator()),
          );
        }
        return buildUploadPage();
      },
    );
  }

  Widget buildUploadPage() {
    return ConstrainedScaffold(
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Category at top
            BlocBuilder<CategoryCubit, PostCategory?>(
              builder: (context, category) {
                return Text(
                  category?.name.replaceFirst(
                        category.name[0],
                        category.name[0].toUpperCase(),
                      ) ??
                      'Politics',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // Main content area fills remaining space
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // Text input takes max space
                    Expanded(
                      child: TextField(
                        controller: _textController,
                        maxLines: null,
                        expands: true, // <-- takes all remaining height
                        maxLength: 480,
                        decoration: InputDecoration(
                          hintText: 'Write something...',
                          border: InputBorder.none,
                          hintStyle: TextStyle(
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Media preview shows dynamically if available
                    if (_selectedImages.isNotEmpty ||
                        _selectedVideos.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: _buildMediaPreview(),
                      ),

                    // Poll preview
                    if (_pollQuestion != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: _buildPollPreview(),
                      ),

                    // Links preview
                    if (_links.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: _buildLinksPreview(),
                      ),

                    // Media pill buttons always at the bottom
                    _buildMediaPillButtons(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Cost + Upload section at bottom
            _buildCostAndUploadSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPreview() {
    return Container(
      padding: const EdgeInsets.all(2),

      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_selectedImages.isNotEmpty) ...[
            // Text(
            //   'Images (${_selectedImages.length})',
            //   style: Theme.of(
            //     context,
            //   ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            // ),
            // const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  final image = _selectedImages[index];
                  return Container(
                    width: 40,
                    margin: const EdgeInsets.only(right: 8),
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: kIsWeb
                                  ? MemoryImage(image.bytes!)
                                  : FileImage(File(image.path!))
                                        as ImageProvider,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              padding: const EdgeInsets.all(4),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 8,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
          if (_selectedVideos.isNotEmpty) ...[
            if (_selectedImages.isNotEmpty) const SizedBox(height: 8),
            // Text(
            //   'Videos (${_selectedVideos.length})',
            //   style: Theme.of(
            //     context,
            //   ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            // ),
            const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedVideos.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 40,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: Colors.black12,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Stack(
                      children: [
                        Center(
                          child: Icon(
                            Icons.play_circle_outline,
                            size: 25,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeVideo(index),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              padding: const EdgeInsets.all(4),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 8,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLinksPreview() {
    return Container(
      padding: const EdgeInsets.all(4),

      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text(
          //   'Links (${_links.length})',
          //   style: Theme.of(
          //     context,
          //   ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          // ),
          // const SizedBox(height: 8),
          ...List.generate(_links.length, (index) {
            final link = _links[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 2),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.link,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      link,
                      style: Theme.of(context).textTheme.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPollPreview() {
    return PollPreviewWidget(
      question: _pollQuestion!,
      onRemove: () {
        setState(() {
          _pollQuestion = null;
        });
        _updateCost();
      },
    );
  }

  Widget _buildMediaPillButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Add Content',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        MediaPillButtonRow(
          onImagesSelected: _handleImagesSelected,
          onVideoSelected: _handleVideoSelected,
          onPollSelected: _handlePollSelected,
          onLinksSelected: () {
            LinkManagementBottomSheet.show(
              context,
              initialLinks: _links,
              onLinksChanged: _updateLinks,
            );
          },
          hasImages: _selectedImages.isNotEmpty,
          hasVideo: _selectedVideos.isNotEmpty,
          hasPoll: _pollQuestion != null,
          hasLinks: _links.isNotEmpty,
        ),
        const SizedBox(height: 4),

        // SizedBox(
        //   width: double.infinity,
        //   child: ElevatedButton.icon(
        //     onPressed: () {
        //       LinkManagementBottomSheet.show(
        //         context,
        //         initialLinks: _links,
        //         onLinksChanged: _updateLinks,
        //       );
        //     },
        //     icon: Icon(_links.isNotEmpty ? Icons.edit : Icons.link),
        //     label: Text(
        //       _links.isNotEmpty ? 'Edit Links (${_links.length})' : 'Add Links',
        //     ),
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: Theme.of(context).colorScheme.tertiary,
        //       foregroundColor: Theme.of(context).colorScheme.surface,
        //     ),
        //   ),
        // ),
      ],
    );
  }

  Widget _buildCostAndUploadSection() {
    return Column(
      children: [
        // Cost display
        BlocBuilder<PostCostCubit, double>(
          builder: (context, cost) {
            return Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Post Cost:',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Text(
                    '\$${cost.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        const SizedBox(height: 12),

        // Upload button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _uploadPost,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.tertiary,
              foregroundColor: Theme.of(context).colorScheme.surface,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: BlocBuilder<PostCubit, PostStates>(
              builder: (context, state) {
                if (state is PostsUploading) {
                  return const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text('Uploading...'),
                    ],
                  );
                }
                return const Text(
                  'Create Post',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
