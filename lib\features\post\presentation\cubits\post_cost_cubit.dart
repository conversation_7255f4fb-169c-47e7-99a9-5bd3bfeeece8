import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/features/post/domain/entities/post.dart';

class PostCostCubit extends Cubit<double> {
  PostCostCubit() : super(0.05); // Default minimum cost

  // Fixed cost - no dynamic calculation
  static const double baseCost = 0.05;

  // Set fixed cost from user's profile selection
  void setFixedCost(double cost) {
    final fixedCost = cost >= baseCost ? cost : baseCost;
    emit(double.parse(fixedCost.toStringAsFixed(3)));
  }

  // Legacy method - now just sets the fixed cost
  void calculateCost({
    required String text,
    int imageCount = 0,
    bool hasVideo = false,
    int linkCount = 0,
    bool hasPoll = false,
  }) {
    // No dynamic calculation - cost remains fixed
    // This method is kept for backward compatibility
  }

  // Legacy method for backward compatibility
  void calculateCostLegacy({
    required String text,
    required PostMediaType mediaType,
    int linkCount = 0,
  }) {
    switch (mediaType) {
      case PostMediaType.images:
        calculateCost(text: text, imageCount: 1, linkCount: linkCount);
        break;
      case PostMediaType.video:
        calculateCost(text: text, hasVideo: true, linkCount: linkCount);
        break;
      case PostMediaType.links:
        calculateCost(text: text, linkCount: linkCount);
        break;
      case PostMediaType.poll:
        // For future poll implementation
        calculateCost(text: text, linkCount: linkCount);
        break;
    }
  }

  void updateCost(double cost) {
    if (cost >= baseCost) {
      emit(cost);
    }
  }

  void resetCost() {
    emit(baseCost);
  }
}
