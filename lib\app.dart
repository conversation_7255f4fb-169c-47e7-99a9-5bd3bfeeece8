import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:social_app_bloc_flutter/common/components/snackbar.dart';
import 'package:social_app_bloc_flutter/features/auth/data/firebase_auth_repo.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:social_app_bloc_flutter/features/auth/presentation/cubits/auth_states.dart';
import 'package:social_app_bloc_flutter/features/post/data/firebase_post_repo.dart';
import 'package:social_app_bloc_flutter/features/post/presentation/cubits/post_cubit.dart';
import 'package:social_app_bloc_flutter/features/profile/data/firebase_profile_repo.dart';
import 'package:social_app_bloc_flutter/features/profile/presentation/cubits/profile_cubit.dart';
import 'package:social_app_bloc_flutter/features/search/data/firebase_search_repo.dart';
import 'package:social_app_bloc_flutter/features/search/presentation/cubits/search_cubit.dart';
import 'package:social_app_bloc_flutter/features/storage/data/firebase_storage_repo.dart';
import 'package:social_app_bloc_flutter/features/wallet/data/firebase_wallet_repo.dart';
import 'package:social_app_bloc_flutter/features/wallet/presentation/cubits/wallet_cubit.dart';
import 'package:social_app_bloc_flutter/features/poll/data/firebase_poll_repo.dart';
import 'package:social_app_bloc_flutter/features/poll/presentation/cubits/poll_cubit.dart';
import 'package:social_app_bloc_flutter/responsive/constrained_scaffold.dart';

import 'package:social_app_bloc_flutter/themes/theme_cubit.dart';

import 'features/auth/presentation/pages/auth_page.dart';
import 'features/home/<USER>/pages/home_page.dart';
import 'features/home/<USER>/cubits/category_cubit.dart';
import 'features/post/presentation/cubits/post_cost_cubit.dart';
import 'features/wallet/presentation/cubits/wallet_balance_cubit.dart';
import 'globals.dart';

/// APP- Root Level
/// ---------------------------------------
/// Repositories: for the database
///  - firebase
///
/// Bloc Providers: For State Management
///  - auth
///  - profile
///  - post
///  - search
///  - theme
///
///  Check Auth State
///    - unauthenticated -> auth page (login/register)
///    - authenticated -> home page

class MainApp extends StatelessWidget {
  // auth repo
  final firebaseAuthRepo = FirebaseAuthRepo();
  // profile repo
  final firebaseProfileRepo = FirebaseProfileRepo();

  // storage repo
  final firebaseStorageRepo = FirebaseStorageRepo();
  // post repo
  final firebasePostRepo = FirebasePostRepo();

  // search repo
  final firebaseSearchRepo = FirebaseSearchRepo();

  // wallet repo
  final firebaseWalletRepo = FirebaseWalletRepo();

  // poll repo
  final firebasePollRepo = FirebasePollRepo();

  MainApp({super.key});
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        // auth Cubit
        BlocProvider<AuthCubit>(
          create: (context) =>
              AuthCubit(authRepo: firebaseAuthRepo)..checkAuth(),
        ),
        // profile  cubit
        BlocProvider<ProfileCubit>(
          create: (context) => ProfileCubit(
            profileRepo: firebaseProfileRepo,
            storageRepo: firebaseStorageRepo,
          ),
        ),
        // post cubit
        BlocProvider<PostCubit>(
          create: (context) => PostCubit(
            postRepo: firebasePostRepo,
            storageRepo: firebaseStorageRepo,
            walletRepo: firebaseWalletRepo,
            pollRepo: firebasePollRepo,
          ),
        ),
        // search cubit
        BlocProvider<SearchCubit>(
          create: (context) => SearchCubit(searchRepo: firebaseSearchRepo),
        ),
        // wallet cubit
        BlocProvider<WalletCubit>(
          create: (context) => WalletCubit(walletRepo: firebaseWalletRepo),
        ),

        BlocProvider<PollCubit>(
          create: (context) => PollCubit(pollRepo: firebasePollRepo),
        ),
        // theme cubit
        BlocProvider<ThemeCubit>(create: (context) => ThemeCubit()),
        // category cubit
        BlocProvider<CategoryCubit>(
          create: (context) =>
              CategoryCubit(profileCubit: context.read<ProfileCubit>()),
        ),
        // post cost cubit
        BlocProvider<PostCostCubit>(create: (context) => PostCostCubit()),
        // wallet balance cubit
        BlocProvider<WalletBalanceCubit>(
          create: (context) =>
              WalletBalanceCubit(walletCubit: context.read<WalletCubit>()),
        ),
      ],

      child: BlocBuilder<ThemeCubit, ThemeData>(
        builder: (context, theme) => MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: theme,

          home: BlocConsumer<AuthCubit, AuthState>(
            builder: (context, authState) {
              print(authState);
              // unauthenticated -> authpage (login/register)
              if (authState is UnAuthenticated) {
                return const AuthPage();
              }
              //   Authenticated -> Home Page
              if (authState is Authenticated) {
                return HomePage(key: homePageKey);
              }
              // loading..
              else {
                return const ConstrainedScaffold(
                  body: Center(child: CircularProgressIndicator()),
                );
              }
            }, // listen for any errors
            listener: (context, authState) {
              if (authState is AuthError) {
                showSnack(context, authState.message);
              }
            },
          ),
        ),
      ),
    );
  }
}
